﻿<%@ Page Title="" Language="C#" MasterPageFile="~/Site.Master" AutoEventWireup="true" CodeBehind="QuanLyChiHo.aspx.cs"
    Inherits="ALSE.QuanLyChiHo" %>

<asp:Content ID="Content1" ContentPlaceHolderID="HeadContent" runat="server">
    <link rel="stylesheet" href="css/custom/QuanLyChiHo.css?v=2025_13062">
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="MainContent" runat="server">

    <div class="main-chiho">
        <div class="color-white text-align-center main-chiho-header">
            <h2>QUẢN LÝ CHI HỘ</h2>
        </div>
    </div>

    <div class="chiho-button margin-bottom-5px">
        <button type="button" class="btn btn-w-200px btn-primary btn-chiho-kehoach"><i class="fa fa-plus"></i>Thêm chi hộ</button>
        <button type="button" class="btn btn-w-200px btn-info btn-chiho-kehoach-excel"><i class="fa fa-file-excel"></i>Thêm chi hộ excel</button>
        <a href="QuanLyChiHoNCC.aspx" type="button" class="btn btn-w-200px btn-success btn-chiho-ncc"><i class="fa fa-address-book"></i>Nhà cung cấp</a>
        <button type="button" class="btn btn-w-200px btn-info btn-chiho-taihoadon"><i class="fa fa-download"></i>Tải hoá đơn</button>
        <button type="button" class="btn btn-w-200px btn-warning btn-chiho-taodntt"><i class="fa fa-plus"></i>Tạo DNTT</button>
        <button type="button" class="btn btn-w-200px btn-danger" id="btn-chiho-hdchuadk">HĐ Chưa ĐK</button>
        <button type="button" class="btn btn-w-200px btn-primary" id="btn-chiho-notncc">HĐ Không NCC</button>
        <button type="button" class="btn btn-w-200px btn-warning" id="btn-chiho-notsohd">HĐ Không Số HĐ</button>
        <button type="button" class="btn btn-w-200px btn-info" id="btn-chiho-taidulieu"><i class="fa fa-download"></i>Tải dữ liệu</button>
        <button type="button" class="btn btn-w-200px btn-success" id="btn-chiho-checkhoadon"><i class="fa fa-check"></i>Check hoá đơn</button>
    </div>

    <div class="main-chiho-body">
        <div id="div-chiho-search">
            <input type="text" class="input-sm input-chiho-search-clear" id="input-chiho-search-awbbill" placeholder="AWBBILL" />
            <input type="text" class="input-sm input-chiho-search-clear" id="input-chiho-search-sohd" placeholder="Số HĐ" />
            <input type="text" class="input-sm input-chiho-search-clear" id="input-chiho-search-sodenghithanhtoan" placeholder="Số đề nghị thanh toán" />
            <select class="input-sm input-chiho-search-clear" id="input-chiho-search-nguoichuyenkhoan">
                <option value="0">ALL</option>
                <option value="195">Trang</option>
                <option value="186">Hiền</option>
                <option value="209">Hạnh</option>
                <!-- <option value="1">Kenji</option> -->
            </select>
            <button type="button" class="btn btn-info" id="btn-chiho-search">
                <i class="fa fa-search"></i>Tìm kiếm (Enter)
            </button>
            <button type="button" class="btn btn-success" id="btn-chiho-search-hdnot-attached">
                <i class="fa fa-search"></i>Tìm kiếm file không đính kèm (Enter)
            </button>
            <button type="button" class="btn btn-danger" id="btn-chiho-search-reset">
                <i class="fa fa-sync"></i>Làm mới (Esc)
            </button>
        </div>
        <table class="table table-bordered" id="tbl-chiho">
            <thead>
                <tr>
                    <td>
                        <input type="checkbox" id="cb-print-all" class="td-checkbox" value="ALL" />
                    </td>
                    <td>NCC</td>
                    <td>Loại hình</td>
                    <td>Khách hàng</td>
                    <td>AWB/BILL</td>
                    <td>Ký hiệu HĐ</td>
                    <td>Số HĐ</td>
                    <td>Người bán</td>
                    <td>Người mua</td>
                    <td>Tên phí</td>
                    <td>Số tiền thanh toán
                        <br>
                        (Sau VAT)</td>
                    <td>Số tiền trước VAT</td>
                    <td>Ngày CK</td>
                    <td>Số đề nghị thanh toán</td>
                    <td>Ghi chú</td>
                    <td>Ngày tạo</td>
                    <td class="td-chucnang">Chức năng</td>
                </tr>
            </thead>
            <tbody>
                <!-- Thêm nhiều hàng hơn theo cách tương tự -->
            </tbody>
        </table>
    </div>


    <div class="modal fade" id="myModalViewChiHo" tabindex="-1" data-keyboard="false" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="h4-chiho-view-tieude"></h4>
                </div>
                <div class="modal-body">
                    <div id="div-chiho-group-button">
                        <div id="div-chiho-button-chucnangkhac">
                        </div>

                        <div id="div-chiho-button-luu">
                        </div>
                    </div>

                    <%--Grid--%>
                    <div class="grid">


                        <%--NCU, loại hình , ngày chuyển khoản--%>
                        <div class="row">
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon " id="span-chiho-loaihinh-modify">Loại
                                                    hình</span>
                                    <select class="form-control input-sm" id="select-chiho-loaihinh">
                                        <option value=""></option>
                                        <option value="IMP">IMPORT</option>
                                        <option value="EXP">EXPORT</option>
                                        <option value="LOG">LOG</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">NCC</span>
                                    <input type="text" class="form-control input-sm input-chiho-clear"
                                        id="input-chiho-ncu" list="sltNCC" />
                                    <datalist class="nobdInput" id="sltNCC">
                                    </datalist>
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Khách hàng</span>
                                    <input type="text" class="form-control input-chiho-clear"
                                        id="input-chiho-khachhang" list="sltKhachHang" />
                                    <datalist class="nobdInput" id="sltKhachHang">
                                    </datalist>


                                </div>
                            </div>


                            <!-- <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Ngày chuyển khoản</span>
                                    <input type="text" class="form-control input-sm input-chiho-ngay datepicker input-chiho-clear" id="input-chiho-ngaychuyenkhoan" />
                                </div>
                            </div> -->
                            <!-- <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Ngày Kết Thúc Kỳ</span>
                                    <input type="text" class="form-control input-sm input-chiho-ngay datepicker input-chiho-clear" id="input-chiho-ngayktky-modify" />
                                </div>
                            </div> -->
                        </div>
                        <%--Ký hiệu hợp đồng , số HĐ , Ngày HĐ --%>
                        <div class="row">
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Ký hiệu HĐ</span>
                                    <input type="text" class="form-control input-sm input-chiho-clear"
                                        id="input-chiho-kihieuhd" />
                                    <input type="text" id="input-chiho-tennguoiban"
                                        style="display: none;">
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Số HĐ</span>
                                    <input type="text" class="form-control input-sm input-chiho-clear"
                                        id="input-chiho-sohd" />
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Ngày HĐ</span>
                                    <input type="text"
                                        class="form-control input-sm input-chiho-ngay datepicker input-chiho-clear"
                                        id="input-chiho-ngayhd" />
                                </div>
                            </div>
                        </div>
                        <%--END Ký hiệu hợp đồng , số HĐ , Ngày HĐ --%>

                        <%--Tên người bán , số tiền thuế , thành tiền--%>
                        <div class="row">

                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Người mua trên
                                                                HĐ</span>
                                    <select type="text" class="form-control input-chiho-clear"
                                        id="input-chiho-nguoimua">
                                        <option value="ALSE">ALSE</option>
                                        <option value="OTHER">OTHER</option>
                                    </select>
                                    <!-- <datalist class="nobdInput" id="sltNguoiMua">
                                                                <option value="ALSE">ALSE</option>
                                                                <option value="OTHER">OTHER</option>
                                                            </datalist> -->
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Tên phí</span>
                                    <input type="text"
                                        class="form-control input-sm input-chiho-clear"
                                        id="input-chiho-phichungtunhap" list="sltphichungtu" />
                                    <datalist class="nobdInput" id="sltphichungtu">
                                        <option class="op-loaihinh-import" value="Phí chứng từ hàng nhập">Phí chứng từ hàng nhập</option>
                                        <option class="op-loaihinh-export" value="Phí an ninh soi chiếu">Phí an ninh soi chiếu</option>
                                        <option class="op-loaihinh-log" value="Phí nâng hạ">Phí nâng hạ</option>
                                        <option class="op-loaihinh-log" value="Phí cơ sở hạ tầng">Phí cơ sở hạ tầng</option>
                                        <option class="op-loaihinh-log" value="Phí Local charge">Phí Local charge</option>
                                        <option class="op-loaihinh-log" value="Phí THC">Phí THC</option>
                                        <option class="op-loaihinh-log" value="Phí Lưu kho">Phí Lưu kho</option>
                                    </datalist>
                                </div>
                            </div>

                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">AWB/BILL</span>
                                    <input type="text" class="form-control input-chiho-clear"
                                        id="input-chiho-awbbill" list="sltawb" />
                                    <datalist class="nobdInput" id="sltawb">
                                    </datalist>
                                </div>
                            </div>
                            <!-- <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Tên người bán</span>
                                    <input type="text" class="form-control input-chiho-clear" id="input-chiho-tennguoiban" />
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Thành tiền</span>
                                    <input type="text" class="form-control input-thanhtoan-number input-chiho-clear" id="input-chiho-thanhtien" />
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Số tiền thuế</span>
                                    <input type="text" class="form-control input-thanhtoan-number input-chiho-clear" id="input-chiho-sotienthue" />
                                </div>
                            </div> -->
                        </div>
                        <%--END Tên người bán , số tiền thuế , thành tiền--%>

                        <%--Khách hàng , AWB/BILL , check--%>
                        <div class="row">
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Số tiền thanh
                                                                        toán</span>
                                    <input type="text"
                                        class="form-control input-thanhtoan-number input-chiho-clear"
                                        id="input-chiho-thanhtien" />
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Số tiền trước
                                                                        VAT</span>
                                    <input type="text"
                                        class="form-control input-thanhtoan-number input-chiho-clear"
                                        id="input-chiho-sotienthue" />
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Số đề nghị
                                                                        thanh toán</span>
                                    <input type="text"
                                        class="form-control  input-chiho-clear"
                                        id="input-chiho-sodenghithanhtoan" />
                                </div>
                            </div>
                        </div>


                        <div class="row">
                            <!-- <div class="form-group col-sm-8">

                                                                <div class="input-group div-chiho-group">
                                                                    <span class="input-group-addon" id="">Tải lên
                                                                        tệp</span>
                                                                    <input type="file" class="form-control"
                                                                        id="input-chiho-uploadfile" />
                                                                </div>
                                                            </div> -->

                            <div class="form-group col-sm-4">
                                <div class="has-warning">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox"
                                                id="checkboxSuccess-dathanhtoanncc"
                                                class="input-dathanhtoanncc" value="">
                                            Đã thanh toán NCC
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Ngày thanh toán NCC</span>
                                    <input type="text"
                                        class="form-control input-sm input-chiho-ngay datepicker input-chiho-clear"
                                        id="input-chiho-ngaythanhtoanncc" disabled />
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Ghi chú</span>
                                    <input type="text"
                                        class="form-control  input-chiho-clear"
                                        id="input-chiho-ghichu" />
                                </div>
                            </div>
                        </div>
                        <!-- <div class="row">
                                                            <div class="form-group col-sm-8">

                                                                <div class="input-group div-chiho-group">
                                                                    <span class="input-group-addon" id="">Tên tệp</span>
                                                                    <input type="text" class="form-control"
                                                                        id="input-chiho-tentep" />
                                                                </div>
                                                            </div>


                                                        </div> -->


                        <div id="drop-area" class="div-upload-group div-drop-area">
                            <!-- <label for="f_UploadImage" class="btn btn-w-200px btn-success btn-sm">
                                                                    <i class="glyphicon glyphicon-plus"></i>&nbsp;&nbsp;&nbsp;Chọn file...
                                                                </label>
                                                                <a class="btn btn-w-200px btn-danger btn-sm" id="a-upload-delete-all"><i
                                                                        class="glyphicon glyphicon-trash"></i>&nbsp;&nbsp;&nbsp;Xóa hết</a> -->
                            <p>Kéo và thả tệp vào đây hoặc nhấp để chọn tệp</p>
                            <label for="f_UploadImage" class="btn btn-w-200px btn-success btn-sm">
                                <i class="glyphicon glyphicon-plus"></i>&nbsp;&nbsp;&nbsp;Chọn file...
                            </label>
                            <input type="file" class="upload" id="f_UploadImage"
                                multiple="multiple"
                                accept="image/jpg, image/png, image/gif, image/jpeg, application/pdf" /><br />
                        </div>
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped active"
                                role="progressbar" aria-valuenow="40" aria-valuemin="0"
                                aria-valuemax="100" style="width: 0%"
                                id="div-upload-process-bar">
                                0%
                            </div>
                        </div>
                        <div id="div-upload-imgzone" class="div-upload-group">
                            <table class="table table-bordered table-responsive"
                                id="tbl-upload-imgzone">
                                <thead>
                                    <tr>
                                        <td>Trạng Thái</td>
                                        <!-- <td>Ảnh</td> -->
                                        <td>Tên File</td>
                                        <td>Kích Thước</td>
                                        <td>Chức năng</td>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>


                        <!-- <div class="row">
                            <div class="form-group col-sm-4">
                                <%-- <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Check</span>
                                    <input type="text" class="form-control input-chiho-clear" id="input-chiho-check" />
                                </div>--%>
                                <div class="has-warning">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" id="checkboxSuccess-check-chiho" class="input-duyetthanhtoan" value="">
                                            Check chi hộ
       
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <%--END Khách hàng , AWB/BILL ,check--%>

                        <%--Trạng thái DNTT , Trạng thái đối chiếu khách , ID
                                                                nhập--%>
                        <!-- <div class="row">
                            <div class="form-group col-sm-4">
                                <%--<div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Trạng thái DNTT</span>
                                    <input type="text" class="form-control input-chiho-clear" id="input-chiho-tt-dntt" />
                                </div>--%>
                                <div class="has-warning">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" id="checkboxSuccess-dntt" class="input-duyetthanhtoan" value="">
                                            Đề nghị thanh toán
       
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <%--  <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Trạng thái đối chiếu khách</span>
                                    <input type="text" class="form-control input-chiho-clear" id="input-chiho-tt-dck" />
                                </div>--%>
                                <div class="has-warning">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" id="checkboxSuccess-doichieukhach" class="input-duyetthanhtoan" value="">
                                            Đối chiếu khách
       
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group col-sm-4">
                                <div class="has-warning">
                                    <div class="checkbox">
                                        <label>
                                            <input type="checkbox" id="checkboxSuccess-duyetthanhtoan" class="input-duyetthanhtoan" value="">
                                            Duyệt thanh toán
       
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div> -->
                        <%--END Trạng thái DNTT , Trạng thái đối chiếu khách ,
                                                                    ID nhập--%>

                        <%--Ghi chú--%>
                        <!-- <div class="row">
                            <div class="form-group col-sm-12">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Ghi Chú</span>
                                    <input type="text" class="form-control input-chiho-clear" id="input-chiho-ghichu" />
                                </div>
                            </div>
                        </div> -->
                        <%--END Ghi chú--%>
                    </div>
                    <%--END Grid--%>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-w-200px btn-primary" id="btn-luu-chiho">Thêm mới</button>
                        <button type="button" class="btn btn-w-200px btn-warning" id="btn-capnhat-chiho" attrid="">
                            Cập
                                        nhật</button>
                        <button type="button" class="btn btn-w-200px btn-default" data-dismiss="modal">Đóng popup</button>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="modal modalFS fade" id="modalQuanLyChiHoExcel" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title">THÊM MỚI CHI HỘ</h4>
                </div>
                <div class="modal-body">
                    <div class="row div-modal-button">
                        <button type="button" class="btn btn-w-200px" data-dismiss="modal">Đóng</button>
                        <button type="button" id="btn-chiho-excel-luu" class="btn btn-w-200px btn-primary"><i class="fa fa-save"></i>Lưu</button>
                    </div>
                    <div id="spreadsheet" class="spreadsheet-width-auto spreadsheet-height-400"></div>
                </div>
                <div class="modal-footer">
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="modalDuyetThanhToanChiHo" data-backdrop="static" data-keyboard="false" tabindex="-1"
        aria-labelledby="staticBackdropLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="staticBackdropLabel">DUYỆT THANH TOÁN</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="container-duyetthanhtoanchiho">
                        <div class="info">
                            <p class="info-title">Chủ tài khoản</p>
                            <div class="info-content">
                                <span id="text-name">HKD SAO THANG TAM</span>
                            </div>

                            <p class="info-title">Số tài khoản</p>
                            <div class="info-content">
                                <span id="text-account">*********</span>
                                <button class="copy-button" type="button"
                                    onclick="copyToClipboard('text-account')">
                                    Sao chép</button>
                            </div>

                            <p class="info-title">Số tiền</p>
                            <div class="info-content">
                                <span class="text-amount" id="text-amount">10,000,000 VND</span>
                                <button class="copy-button" type="button"
                                    onclick="copyToClipboard('text-amount')">
                                    Sao chép</button>
                            </div>

                            <p class="info-title">Nội dung (bắt buộc)</p>
                            <div class="info-content">
                                <span class="text-content" id="text-content">DGM223285565</span>
                                <button class="copy-button" type="button"
                                    onclick="copyToClipboard('text-content')">
                                    Sao chép</button>
                            </div>
                        </div>

                        <div class="qr-section">
                            <img id="imgQRCode" alt="QR Code" class="qr-code">
                        </div>

                        <%--<button class="download-btn">Tải QR thanh toán</button>--%>

                        <div class="expiry-info">
                            <span class="text-content"></span>
                            <br>
                            <span class="text-amount"></span>VND
                                    <%--<br>
                                        Hết hạn : 10:48 14/11/2024--%>
                        </div>

                        <p class="note">(*) Đây là mã QR chỉ sử dụng 1 lần duy nhất</p>
                        <p class="instructions">
                            Hướng dẫn chuyển khoản nhanh sử dụng QR Code trên <span
                                class="highlight">ALSE</span>
                        </p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-w-200px btn-secondary" data-dismiss="modal">Đóng</button>
                    <button type="button" class="btn btn-w-200px btn-primary" id="btn-duyetthanhtoan" attrid="">Duyệt</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Upload -->
    <div class="modal fade" id="myModalUpload" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog " role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="">Upload File: <span id="span-upload-tilte"
                        class="color-red font-weight-bold"></span></h4>
                </div>
                <div class="modal-body">
                </div>
                <div class="modal-footer">

                    <button type="button" class="btn btn-w-200px btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal -->

    <!-- Modal Tải Hoá Đơn -->
    <div class="modal fade" id="modalTaiHoaDon" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="modalTaiHoaDonLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="modalTaiHoaDonLabel">Tải Hoá Đơn</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="multiTextBoxAWB">Nhập nhiều AWB:</label>
                        <textarea class="form-control" id="multiTextBoxAWB" rows="10" placeholder="Dán các AWB vào đây, mỗi dòng một AWB"></textarea>
                        <table class="table table-bordered" id="tbl-taihoadon">
                            <thead>
                                <tr>
                                    <td>Trạng thái</td>
                                    <td>AWBBILL</td>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-w-200px btn-primary" id="btnDownloadHoaDon">Download</button>
                    <button type="button" class="btn btn-w-200px btn-default" data-dismiss="modal">Đóng</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal Tạo đề nghị thanh toán -->
    <div class="modal fade modalFS" id="modalTaoDNTT" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="modalTaiHoaDonLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="modalTaoDNTTLabel">Tạo đề nghị thanh toán</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <div class="row da-duyet-an">
                            <div class="form-group col-sm-2">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon ">Loại hình</span>
                                    <select class="form-control input-sm" id="select-dntt-loaihinh">
                                        <option value="ALL">ALL</option>
                                        <option value="IMP">IMPORT</option>
                                        <option value="EXP">EXPORT</option>
                                        <option value="LOG">LOG</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group col-sm-2">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon ">Người CK</span>
                                    <select class="form-control input-sm" id="select-dntt-nguoichuyenkhoan">
                                        <option value="0">ALL</option>
                                        <option value="195">Trang</option>
                                        <option value="186">Hiền</option>
                                        <option value="209">Hạnh</option>
                                        <option value="20">Tú</option>
                                        <!-- <option value="1">Kenji</option> -->
                                    </select>
                                </div>
                            </div>
                            <div class="form-group col-sm-2">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Khách hàng</span>
                                    <input type="text" class="form-control input-chiho-clear"
                                        id="input-dntt-khachhang" list="sltKhachHangDNTT" />
                                    <datalist class="nobdInput" id="sltKhachHangDNTT">
                                    </datalist>
                                </div>
                            </div>

                            <div class="form-group col-sm-4">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Nội dung thanh toán</span>
                                    <input type="text" class="form-control input-chiho-clear"
                                        id="input-dntt-noidungthanhtoan" />
                                </div>
                            </div>
                            <div class="form-group col-sm-2">
                                <div class="input-group div-chiho-group">
                                    <span class="input-group-addon" id="">Người đề nghị</span>
                                    <input type="text" class="form-control input-chiho-clear" disabled
                                        id="input-dntt-nguoidenghi" />
                                </div>
                            </div>
                        </div>
                        <div class="table-container">
                            <table class="table table-bordered" id="tbl-dntt-chiho">
                                <thead>
                                    <tr>
                                        <td>
                                            <input type="checkbox" id="cb-dntt-select-all" class="td-checkbox" value="ALL" />
                                        </td>
                                        <td>Số HĐ</td>
                                        <td>Khách hàng</td>
                                        <td>AWB/BILL</td>
                                        <td>Ký hiệu HĐ</td>
                                        <td>Người bán</td>
                                        <td>Tên phí</td>
                                        <td>Số tiền Sau VAT</td>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            <table class="table table-bordered table-responsive" id="tbl-dntt-chitiet">
                                <thead>
                                    <tr>
                                        <th rowspan="2">STT</th>
                                        <th colspan="3">CHỨNG TỪ</th>
                                        <th rowspan="2">TÊN NGƯỜI BÁN</th>
                                        <th rowspan="2">Diễn giải</th>
                                        <th rowspan="2">THÀNH TIỀN</th>
                                        <th rowspan="2">Thao Tác</th>
                                    </tr>
                                    <tr>
                                        <th>CHỨNG TỪ</th>
                                        <th>SỐ</th>
                                        <th>NGÀY THÁNG</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Dữ liệu sẽ được thêm vào đây -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" dnttid="" id="btnDuyetDNTT"><i class="fa fa-check"></i>Duyệt DNTT</button>
                    <button type="button" class="btn btn-w-200px btn-danger" dnttid="" id="btnTaiHDDNTT"><i class="fa fa-download"></i>Tải Hoá Đơn</button>
                    <button type="button" class="btn btn-w-200px btn-warning" dnttid="" id="btnTaiDNTTExcel"><i class="fa fa-file-excel"></i>Tải DNTT Excel</button>
                    <button type="button" class="btn btn-w-200px btn-info" dnttid="" id="btnInDNTT"><i class="fa fa-print"></i>In</button>
                    <button type="button" class="btn btn-w-200px btn-primary da-duyet-an" dnttid="" id="btnSaveDNTT"><i class="fa fa-save"></i>Lưu và In</button>
                    <button type="button" class="btn btn-w-200px btn-default" data-dismiss="modal"><i class="fa fa-close"></i>Đóng</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal Tải Hoá Đơn -->
    <div class="modal fade" id="modalTaiDuLieu" tabindex="-1" data-backdrop="static" data-keyboard="false" role="dialog" aria-labelledby="modalTaiHoaDonLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                    <h4 class="modal-title" id="modalTaiDuLieuLabel">Tải Dữ liệu</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="form-group col-sm-4">
                            <div class="input-group div-chiho-group">
                                <span class="input-group-addon" id="">Khách hàng</span>
                                <input type="text" class="form-control input-chiho-clear"
                                    id="input-taidulieu-khachhang" list="slt-taidulieu-khachhang" />
                                <datalist class="nobdInput" id="slt-taidulieu-khachhang">
                                </datalist>
                            </div>
                        </div>

                        <div class="form-group col-sm-4">
                            <div class="input-group div-chiho-group">
                                <span class="input-group-addon" id="">Từ ngày</span>
                                <input type="text"
                                    class="form-control input-sm input-chiho-ngay datepicker input-chiho-clear"
                                    id="input-taidulieu-tungay" />
                            </div>
                        </div>
                        <div class="form-group col-sm-4">
                            <div class="input-group div-chiho-group">
                                <span class="input-group-addon" id="">Đến ngày</span>
                                <input type="text"
                                    class="form-control input-sm input-chiho-ngay datepicker input-chiho-clear"
                                    id="input-taidulieu-denngay" />
                            </div>
                        </div>

                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-w-200px btn-primary" id="btnDownloadData">Download</button>
                        <button type="button" class="btn btn-w-200px btn-default" data-dismiss="modal">Đóng</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Modal Check Hoá Đơn -->
    <div class="modal fade modalFS" id="myModalViewCheckHoaDon" tabindex="-1" data-keyboard="false" role="dialog" aria-labelledby="myModalLabel">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="h4-CheckHoaDon-view-tieude">Check Hoá Đơn</h4>
                </div>
                <div class="modal-body">


                    <%--Grid--%>
                    <div class="grid">
                        <div id="drop-area-check-hoa-don" class="div-upload-group div-drop-area">

                            <p>Kéo và thả tệp vào đây hoặc nhấp để chọn tệp</p>
                            <label for="f_UploadCheckHoaDon" class="btn btn-w-200px btn-success btn-sm">
                                <i class="glyphicon glyphicon-plus"></i>&nbsp;&nbsp;&nbsp;Chọn file...
                            </label>
                            <input type="file" class="upload" id="f_UploadCheckHoaDon"
                                accept="application/vnd.ms-excel, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" /><br />
                        </div>
                        <div class="div-button">
                            <div class="div-button-middle">
                                <button type="button" class="btn btn-w-300px btn-danger" id="btn-luu-CheckHoaDon">Check Hoá Đơn</button>
                            </div>
                            <!-- <div class="div-button-right">
                                    <button type="button" class="btn btn-w-200px btn-success btn-CheckHoaDon" id="btn-CheckHoaDon-DaCapNhat">HĐ Đã Cập nhật</button>
                                    <button type="button" class="btn btn-w-200px btn-warning btn-CheckHoaDon" id="btn-CheckHoaDon-KhongCoThongTin">HĐ Không có thông tin</button>
                                </div> -->
                            </div>
                            <div class="div-button">
                                <div class="div-button-middle">
                                    <button type="button" class="btn btn-w-300px btn-success btn-CheckHoaDon" id="btn-CheckHoaDon-DaCheckHoaDon">HĐ Đã Check</button>
                                    <button type="button" class="btn btn-w-300px btn-success btn-CheckHoaDon" id="btn-CheckHoaDon-DaCapNhat">HĐ Đã Cập nhật</button>
                                    <button type="button" class="btn btn-w-300px btn-warning btn-CheckHoaDon" id="btn-CheckHoaDon-SaiThanhTien">HĐ Sai thành tiền</button>
                                    <button type="button" class="btn btn-w-300px btn-primary btn-CheckHoaDon" id="btn-CheckHoaDon-NguoiMuaKhongPhaiALSE">HĐ Người mua không phải ALSE</button>
                                    <button type="button" class="btn btn-w-300px btn-danger btn-CheckHoaDon" id="btn-CheckHoaDon-KhongTonTaiSHDvaMST">HĐ Không tồn tại SHĐ và MST</button>
                                </div> 
                            </div>
                            <div id="div-upload-check-hoa-don" class="div-upload-group">
                                <table class="table table-bordered table-responsive"
                                    id="tbl-upload-check-hoa-don">
                                    <thead>
                                        <tr>
                                            <td rowspan="2">STT</td>
                                            <td rowspan="2">Ký hiệu hóa đơn</td>
                                            <td rowspan="2">Số hóa đơn</td>
                                            <td rowspan="2">Ngày lập</td>
                                            <td rowspan="2">MST người bán/MST người xuất hàng</td>
                                            <td rowspan="2">Tên người bán/Tên người xuất hàng</td>
                                            <td colspan="2">CSDL</td>
                                            <td colspan="2">Excel</td>
                                            <td rowspan="2">Trạng thái</td>
                                        </tr>
                                        <tr>
                                            <td>Tổng tiền chưa thuế</td>
                                            <td>Tổng tiền thanh toán</td>
                                            <td>Tổng tiền chưa thuế</td>
                                            <td>Tổng tiền thanh toán</td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                </table>
                            </div>



                    </div>
                    <%--END Grid--%>
                    <div class="modal-footer">

                        <button type="button" class="btn btn-w-200px btn-default" data-dismiss="modal">Đóng popup</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="js/custom/QuanLyChiHo.js?v=2025_2306"></script>

</asp:Content>
