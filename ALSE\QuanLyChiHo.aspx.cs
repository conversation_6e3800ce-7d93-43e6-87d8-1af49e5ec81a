using System;
using System.Collections.Generic;
using System.Data;
using System.Web;
using System.Web.Services;
using System.Web.Script.Services;
using System.Net;
using System.IO;
using System.Configuration;
using Ionic.Zip;
using System.Linq;
using System.Web.Caching;
using OfficeOpenXml;
using Spire.Xls;
using OfficeOpenXml.Style;

namespace ALSE
{
    public partial class QuanLyChiHo : System.Web.UI.Page
    {
        private static cs.SIUD siud = new cs.SIUD();
        private static cs.Pr pr = new cs.Pr();
        private static DataSet ds = new DataSet();
        public static string userId = "";
        private static FtpWebRequest ftpRequest = null;
        private static FtpWebResponse ftpResponse = null;
        private static Stream ftpStream = null;

        private static string user = ConfigurationManager.AppSettings["FTPUsername"];
        private static string pass = ConfigurationManager.AppSettings["FTPPassword"];
        private static string ftp = ConfigurationManager.AppSettings["FTPServer"] + "ChiHo/";
        private static string folder = "";
        //private static string userid = System.Web.HttpContext.Current.Session["WebUID"].ToString();
        protected void Page_Load(object sender, EventArgs e)
        {
            //var apiRequestBank = new RequestBank();
            //apiRequestBank.acqId = 970407;
            //apiRequestBank.accountNo = **********;
            //apiRequestBank.accountName = "DUNG PHUNG";
            //apiRequestBank.amount = 200000;
            //apiRequestBank.format = "text";
            //apiRequestBank.template = "compact";
            //apiRequestBank.addInfo = "NGU NO VUA THOI";
            //var jsonRequest = JsonConvert.SerializeObject(apiRequestBank);

            ////user restsharf
            //var client = new RestClient("https://api.vietqr.io/v2/generate");
            //var request = new RestRequest();

            //request.Method = Method.POST;
            //request.AddHeader("Accept", "application/json");
            //request.Parameters.Clear();
            //request.AddParameter("application/json", apiRequestBank, ParameterType.RequestBody);

            //var response = client.Execute(request);
            //var content = response.Content; // raw content as string
        }

        #region Load

        [WebMethod]
        public static List<KhachHangChiHo> reKhachHang(cs.HamDungChung.ajaxGet ajaxGet)
        {
            List<KhachHangChiHo> list = new List<KhachHangChiHo>();
            pr.ChiHoKhachHangParameter("0"
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           );
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_KhachHang", "swp", pr.pname, pr.pvalue);
            if (ds.Tables[0].Rows.Count > 0)
            {
                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    list.Add(new KhachHangChiHo
                    {
                        KhachHang = ds.Tables[0].Rows[i]["KhachHang"].ToString().Trim(),
                        LoaiHinh = ds.Tables[0].Rows[i]["LoaiHinh"].ToString().Trim(),
                    });
                }
            }
            return list;
        }

        [WebMethod]
        public static dataChiHo ReChiHo(cs.HamDungChung.ajaxGet ajaxGet)
        {
            string userid = System.Web.HttpContext.Current.Session["WebUID"].ToString();
            pr.ChiHoParameter("0"
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , userid
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            );
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo", "swp", pr.pname, pr.pvalue);
            return DataSetToChiHo(ds);
        }
        [WebMethod]
        public static dataChiHo ReChiHoTimKiem(cs.HamDungChung.ajaxGet4 ajaxGet4)
        {
            string iu = "1";
            if(ajaxGet4.get1 == "Search")
            {
                iu = "1";
            }
            if (ajaxGet4.get1 == "HDChuaDK")
            {
                iu = "2";
            }
            if (ajaxGet4.get1 == "NguoiChuyenKhoan")
            {
                iu = "7";
            }
            if (ajaxGet4.get1 == "NotNCC")
            {
                iu = "9";
            }
            if (ajaxGet4.get1 == "NotSoHD")
            {
                iu = "10";
            }
            if (ajaxGet4.get1 == "SearchNotAttach")
            {
                iu = "11";
            }
            pr.Search4Parameter(iu
                           , ajaxGet4.get2
                           , ajaxGet4.get3
                           , ajaxGet4.get4
                           , ""
                           ); 
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_TimKiem", "swp", pr.pname, pr.pvalue);
            return DataSetToChiHo(ds);
        }
        [WebMethod]
        public static string TimKiemAWBBill(cs.HamDungChung.ajaxGet4 ajaxGet4)
        {
            string iu = "3";
           
            pr.Search4Parameter(iu
                           , ajaxGet4.get1
                           , ajaxGet4.get2
                           , ajaxGet4.get3
                           , ""
                           ); 
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_TimKiem", "swp", pr.pname, pr.pvalue);
            if(ds != null && ds.Tables.Count > 0 && ds.Tables[0].Rows.Count > 0){
                return ds.Tables[0].Rows[0]["bill_awb"].ToString();
            }
            return "0";
        }
        [WebMethod]
        public static string TaiDuLieu(cs.HamDungChung.ajaxGet4 ajaxGet4)
        {
            string iu = "4";
           
            pr.Search4Parameter(iu
                           , ajaxGet4.get1
                           , ajaxGet4.get2
                           , ajaxGet4.get3
                           , ""
                           ); 
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_TimKiem", "swp", pr.pname, pr.pvalue);
            dataChiHo dataChiHo = DataSetToChiHo(ds);
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string fileName = $"files_{timestamp}.xlsx";
            
            WebClient request = new WebClient();
            string url = "";
            byte[] newFileData = null;
         
            request.Credentials = new NetworkCredential(cs.ftp._user, cs.ftp._pass);
            url = ftp + "Templates" + "/" + "TaiDuLieu.xlsx";
            try
            {
                newFileData = request.DownloadData(url);
            }
            catch (WebException ex)
            {
                throw new Exception((ex.Response as FtpWebResponse).StatusDescription);
            }
            using (var excelPackage = new ExcelPackage(new MemoryStream(newFileData)))
            {
                excelPackage.Workbook.Properties.Author = "KENJI";
                excelPackage.Workbook.Properties.Title = "Tải dữ liệu";
                excelPackage.Workbook.Properties.Comments = "";
                var workSheet1 = excelPackage.Workbook.Worksheets[1];
                int dulieuCount = dataChiHo.chiHos.Count;
                int startRow = 2;
                int currentRow = 2;

                if (dulieuCount > 0)
                {

                    // bắt đầu từ dòng 19
                    int tableRow = 1;

                    foreach (ChiHo item in dataChiHo.chiHos)
                    {
                        workSheet1.Cells["A" + currentRow.ToString()].Value = tableRow;
                        workSheet1.Cells["B" + currentRow.ToString()].Value = item.NCU;
                        workSheet1.Cells["C" + currentRow.ToString()].Value = item.LoaiHinh;
                        workSheet1.Cells["D" + currentRow.ToString()].Value = item.KhachHang;
                        workSheet1.Cells["E" + currentRow.ToString()].Formula = "=CONCATENATE(\"" + item.AWBBILL + "\",\"\" )";
                        //returnDateTimeddMMyyyy(item.NgayHD)[0];
                        //workSheet1.Cells["E" + currentRow.ToString()].Style.Numberformat.Format = "dd/MM/yyyy";
                        workSheet1.Cells["F" + currentRow.ToString()].Value = item.KiHieuHD;
                        //workSheet1.Cells["F" + currentRow.ToString()].Style.WrapText = true;
                        workSheet1.Cells["G" + currentRow.ToString()].Formula = "=CONCATENATE(\"" + item.SoHD + "\",\"\" )";
                        workSheet1.Cells["H" + currentRow.ToString()].Value = item.TenNguoiBan;
                        workSheet1.Cells["I" + currentRow.ToString()].Value = item.HoaDonKhach;
                        workSheet1.Cells["J" + currentRow.ToString()].Value = item.PhiChungTuNhap;
                        workSheet1.Cells["K" + currentRow.ToString()].Formula = "=Value(" + item.ThanhTien + ")";
                        workSheet1.Cells["K" + currentRow.ToString()].Style.Numberformat.Format = "#,##0";
                        workSheet1.Cells["L" + currentRow.ToString()].Formula = "=Value(" + item.SoTruocThue + ")";
                        workSheet1.Cells["L" + currentRow.ToString()].Style.Numberformat.Format = "#,##0";
                        workSheet1.Cells["M" + currentRow.ToString()].Value = returnDateTimeddMMyyyy(item.NgayThanhToanNCC.ToString())[0];
                        workSheet1.Cells["M" + currentRow.ToString()].Style.Numberformat.Format = "dd/MM/yyyy";
                        workSheet1.Cells["N" + currentRow.ToString()].Formula = "=CONCATENATE(\"" + item.SoDeNghiThanhToan + "\",\"\" )";
                        workSheet1.Cells["O" + currentRow.ToString()].Value = item.GhiChu;
                        currentRow++;
                        tableRow++;
                    }


                    using (var ranger = workSheet1.Cells["A" + (startRow -1).ToString() + ":O" + (currentRow -1).ToString()])
                    {
                        ranger.Style.Font.Name = "Times New Roman";
                        ranger.Style.Font.Size = 11;
                        ranger.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        ranger.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        ranger.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        ranger.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        ranger.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                        ranger.Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;
                        ranger.AutoFitColumns(5);
                    }

                }
                excelPackage.Save();
                var stream = excelPackage.Stream;
                using (MemoryStream ms = stream as MemoryStream)
                {
                    byte[] fileBytes = ms.ToArray(); // Chuyển đổi MemoryStream thành byte[]
                    HttpRuntime.Cache.Insert(
                        fileName,
                        fileBytes, // Lưu trữ byte[] thay vì MemoryStream
                        null,
                        Cache.NoAbsoluteExpiration,
                        TimeSpan.FromMinutes(10));
                }
                return fileName;
            }
            
            
            
        }
        public static dataChiHo DataSetToChiHo(DataSet ds)
        {
            dataChiHo dataChiHo = new dataChiHo();
            List<ChiHo> listChiHo = new List<ChiHo>();
            if (ds.Tables[0].Rows.Count > 0)
            {
                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    listChiHo.Add(new ChiHo
                    {
                        Id = ds.Tables[0].Rows[i]["Id"].ToString(),
                        NCU = ds.Tables[0].Rows[i]["NCU"].ToString(),
                        LoaiHinh = ds.Tables[0].Rows[i]["LoaiHinh"].ToString(),
                        NgayCK = ds.Tables[0].Rows[i]["NgayCK"].ToString(),
                        KhachHang = ds.Tables[0].Rows[i]["KhachHang"].ToString(),
                        AWBBILL = ds.Tables[0].Rows[i]["AWBBILL"].ToString(),
                        KiHieuHD = ds.Tables[0].Rows[i]["KiHieuHD"].ToString(),
                        TenNguoiBan = ds.Tables[0].Rows[i]["TenNguoiBan"].ToString(),
                        PhiChungTuNhap = ds.Tables[0].Rows[i]["PhiChungTuNhap"].ToString(),
                        SoTruocThue = ds.Tables[0].Rows[i]["SoTruocThue"].ToString(),
                        ThanhTien = ds.Tables[0].Rows[i]["ThanhTien"].ToString(),
                        HoaDonKhach = ds.Tables[0].Rows[i]["HoaDonKhach"].ToString(),
                        Check_ChiHo = ds.Tables[0].Rows[i]["Check_ChiHo"].ToString(),
                        GhiChu = ds.Tables[0].Rows[i]["GhiChu"].ToString(),
                        TrangThaiDNTT = ds.Tables[0].Rows[i]["TrangThaiDNTT"].ToString(),
                        IdNhap = ds.Tables[0].Rows[i]["IdNhap"].ToString(),
                        SoHD = ds.Tables[0].Rows[i]["SoHD"].ToString(),
                        NgayHD = ds.Tables[0].Rows[i]["NgayHD"].ToString(),
                        TrangThaiDoiChieuKhach = ds.Tables[0].Rows[i]["TrangThaiDoiChieuKhach"].ToString(),
                        DuyetThanhToan = ds.Tables[0].Rows[i]["DuyetThanhToan"].ToString(),
                        SoDeNghiThanhToan = ds.Tables[0].Rows[i]["SoDeNghiThanhToan"].ToString(),
                        FileDinhKem = ds.Tables[0].Rows[i]["FileDinhKem"].ToString(),
                        ThanhToanNCC = ds.Tables[0].Rows[i]["ThanhToanNCC"].ToString(),
                        NgayThanhToanNCC = ds.Tables[0].Rows[i]["NgayThanhToanNCC"].ToString(),
                        NguoiDuyetThanhToanNCC = ds.Tables[0].Rows[i]["NguoiDuyetThanhToanNCC"].ToString(),
                        Check_HoaDon = ds.Tables[0].Rows[i]["Check_HoaDon"].ToString(),
                        NgayTao = ds.Tables[0].Rows[i]["NgayTao"].ToString()
                    });
                }
            }
            string hdChuaDK = "0";
            string HdKhongNCC = "0";
            string HdKhongSoHD = "0";
            if (ds.Tables[1].Rows.Count > 0)
            {
                hdChuaDK = ds.Tables[1].Rows[0]["HdChuaDK"].ToString();
            }

            if (ds.Tables[2].Rows.Count > 0)
            {
                HdKhongNCC = ds.Tables[2].Rows[0]["HdKhongNCC"].ToString();
            }

            if (ds.Tables[3].Rows.Count > 0)
            {
                HdKhongSoHD = ds.Tables[3].Rows[0]["HdKhongSoHD"].ToString();
            }
            dataChiHo.HdChuaDK = hdChuaDK;
            dataChiHo.chiHos = listChiHo;
            dataChiHo.HdKhongNCC = HdKhongNCC;
            dataChiHo.HdKhongSoHD = HdKhongSoHD;
            return dataChiHo;
        }

        [WebMethod]
        public static List<ChiHo> ReDNTTChiHo(cs.HamDungChung.ajaxGet3 ajaxGet3 )
        {
            List<ChiHo> list = new List<ChiHo>();
            pr.ChiHoParameter("8"
                            , ""
                            , ""
                            , ajaxGet3.get1
                            , ""
                            , ajaxGet3.get3
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ajaxGet3.get2  // nguoi chuyen khoan
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            );
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo", "swp", pr.pname, pr.pvalue);
            if (ds.Tables[0].Rows.Count > 0)
            {
                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    list.Add(new ChiHo
                    {
                        Id = ds.Tables[0].Rows[i]["Id"].ToString(),
                        NCU = ds.Tables[0].Rows[i]["NCU"].ToString(),
                        LoaiHinh = ds.Tables[0].Rows[i]["LoaiHinh"].ToString(),
                        NgayCK = ds.Tables[0].Rows[i]["NgayCK"].ToString(),
                        KhachHang = ds.Tables[0].Rows[i]["KhachHang"].ToString(),
                        AWBBILL = ds.Tables[0].Rows[i]["AWBBILL"].ToString(),
                        KiHieuHD = ds.Tables[0].Rows[i]["KiHieuHD"].ToString(),
                        TenNguoiBan = ds.Tables[0].Rows[i]["TenNguoiBan"].ToString(),
                        PhiChungTuNhap = ds.Tables[0].Rows[i]["PhiChungTuNhap"].ToString(),
                        SoTruocThue = ds.Tables[0].Rows[i]["SoTruocThue"].ToString(),
                        ThanhTien = ds.Tables[0].Rows[i]["ThanhTien"].ToString(),
                        HoaDonKhach = ds.Tables[0].Rows[i]["HoaDonKhach"].ToString(),
                        Check_ChiHo = ds.Tables[0].Rows[i]["Check_ChiHo"].ToString(),
                        GhiChu = ds.Tables[0].Rows[i]["GhiChu"].ToString(),
                        TrangThaiDNTT = ds.Tables[0].Rows[i]["TrangThaiDNTT"].ToString(),
                        IdNhap = ds.Tables[0].Rows[i]["IdNhap"].ToString(),
                        SoHD = ds.Tables[0].Rows[i]["SoHD"].ToString(),
                        NgayHD = ds.Tables[0].Rows[i]["NgayHD"].ToString(),
                        TrangThaiDoiChieuKhach = ds.Tables[0].Rows[i]["TrangThaiDoiChieuKhach"].ToString(),
                        DuyetThanhToan = ds.Tables[0].Rows[i]["DuyetThanhToan"].ToString(),
                        SoDeNghiThanhToan = ds.Tables[0].Rows[i]["SoDeNghiThanhToan"].ToString(),
                        FileDinhKem = ds.Tables[0].Rows[i]["FileDinhKem"].ToString(),
                        ThanhToanNCC = ds.Tables[0].Rows[i]["ThanhToanNCC"].ToString(),
                        NgayThanhToanNCC = ds.Tables[0].Rows[i]["NgayThanhToanNCC"].ToString(),
                        NguoiDuyetThanhToanNCC = ds.Tables[0].Rows[i]["NguoiDuyetThanhToanNCC"].ToString(),
                        Check_HoaDon = ds.Tables[0].Rows[i]["Check_HoaDon"].ToString(),
                        NgayTao = ds.Tables[0].Rows[i]["NgayTao"].ToString()
                    });
                }
            }
            return list;
        }


        [WebMethod]
        public static ChiHo reChiHoById(cs.HamDungChung.ajaxGet ajaxGet)
        {
            ChiHo chiHo = new ChiHo();
            pr.ChiHoParameter("4"           
                        , ajaxGet.get
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        , ""
                        );
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo", "swp", pr.pname, pr.pvalue);
            if (ds.Tables[0].Rows.Count > 0)
            {
                chiHo.Id                     = ds.Tables[0].Rows[0]["Id"].ToString();
                chiHo.NCU                    = ds.Tables[0].Rows[0]["NCU"].ToString();
                chiHo.LoaiHinh               = ds.Tables[0].Rows[0]["LoaiHinh"].ToString();
                chiHo.NgayCK                 = ds.Tables[0].Rows[0]["NgayCK"].ToString();
                chiHo.KhachHang              = ds.Tables[0].Rows[0]["KhachHang"].ToString();
                chiHo.AWBBILL                = ds.Tables[0].Rows[0]["AWBBILL"].ToString();
                chiHo.KiHieuHD               = ds.Tables[0].Rows[0]["KiHieuHD"].ToString();
                chiHo.TenNguoiBan            = ds.Tables[0].Rows[0]["TenNguoiBan"].ToString();
                chiHo.PhiChungTuNhap         = ds.Tables[0].Rows[0]["PhiChungTuNhap"].ToString();
                chiHo.SoTruocThue            = ds.Tables[0].Rows[0]["SoTruocThue"].ToString();
                chiHo.ThanhTien              = ds.Tables[0].Rows[0]["ThanhTien"].ToString();
                chiHo.HoaDonKhach            = ds.Tables[0].Rows[0]["HoaDonKhach"].ToString();
                chiHo.Check_ChiHo            = ds.Tables[0].Rows[0]["Check_ChiHo"].ToString();
                chiHo.GhiChu                 = ds.Tables[0].Rows[0]["GhiChu"].ToString();
                chiHo.TrangThaiDNTT          = ds.Tables[0].Rows[0]["TrangThaiDNTT"].ToString();
                chiHo.IdNhap                 = ds.Tables[0].Rows[0]["IdNhap"].ToString();
                chiHo.SoHD                   = ds.Tables[0].Rows[0]["SoHD"].ToString();
                chiHo.NgayHD                 = ds.Tables[0].Rows[0]["NgayHD"].ToString();
                chiHo.TrangThaiDoiChieuKhach = ds.Tables[0].Rows[0]["TrangThaiDoiChieuKhach"].ToString();
                chiHo.DuyetThanhToan         = ds.Tables[0].Rows[0]["DuyetThanhToan"].ToString();
                chiHo.SoDeNghiThanhToan      = ds.Tables[0].Rows[0]["SoDeNghiThanhToan"].ToString();
                chiHo.ThanhToanNCC           = ds.Tables[0].Rows[0]["ThanhToanNCC"].ToString();
                chiHo.NgayThanhToanNCC       = ds.Tables[0].Rows[0]["NgayThanhToanNCC"].ToString();
                chiHo.NguoiDuyetThanhToanNCC = ds.Tables[0].Rows[0]["NguoiDuyetThanhToanNCC"].ToString();
                chiHo.Check_HoaDon           = ds.Tables[0].Rows[0]["Check_HoaDon"].ToString();
            }
            return chiHo;
        }

        [WebMethod]
        public static List<DNTT> ReDNTT(cs.HamDungChung.ajaxGet ajaxGet)
        {
            List<DNTT> list = new List<DNTT>(); 
            pr.DNTTParameter("0"
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            , ""
                            );
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_DNTT", "swp", pr.pname, pr.pvalue);
            if (ds.Tables[0].Rows.Count > 0)
            {
                for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                {
                    list.Add(new DNTT
                    {
                        Id = ds.Tables[0].Rows[i]["Id"].ToString(),
                        KhachHang = ds.Tables[0].Rows[i]["KhachHang"].ToString(),
                        NoiDungThanhToan = ds.Tables[0].Rows[i]["NoiDungThanhToan"].ToString(),
                        GhiChu = ds.Tables[0].Rows[i]["GhiChu"].ToString(),
                        DaDuyet = ds.Tables[0].Rows[i]["DaDuyet"].ToString(),
                        NgayDuyet = ds.Tables[0].Rows[i]["NgayDuyet"].ToString(),
                        NguoiDuyet = ds.Tables[0].Rows[i]["NguoiDuyet"].ToString(),
                    });
                }
            }
            return list;
        }
        [WebMethod]
        public static DNTT ReDNTTId(cs.HamDungChung.ajaxGet ajaxGet)
        {
            DNTT dntt = new DNTT(); 
            List<DNTTChiTiet> listChiTiet = new List<DNTTChiTiet>();
            
             pr.DNTTParameter("4"
                                , ajaxGet.get
                                , ""
                                , ""
                                , ""
                                , ""
                                , ""
                                , ""
                                );
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_DNTT", "swp", pr.pname, pr.pvalue);
                    
            if (ds.Tables[0].Rows.Count > 0)
            {
                dntt.Id = ds.Tables[0].Rows[0]["Id"].ToString();
                dntt.KhachHang = ds.Tables[0].Rows[0]["KhachHang"].ToString();
                dntt.NoiDungThanhToan = ds.Tables[0].Rows[0]["NoiDungThanhToan"].ToString();
                dntt.GhiChu = ds.Tables[0].Rows[0]["GhiChu"].ToString();
                dntt.DaDuyet = ds.Tables[0].Rows[0]["DaDuyet"].ToString();
                dntt.NgayDuyet = ds.Tables[0].Rows[0]["NgayDuyet"].ToString();
                dntt.NguoiDuyet = ds.Tables[0].Rows[0]["NguoiDuyet"].ToString();
            }
            if(ds.Tables[1].Rows.Count > 0  )
            {
                for (int i = 0; i < ds.Tables[1].Rows.Count; i++)
                {
                    listChiTiet.Add(new DNTTChiTiet
                    {
                        Id = ds.Tables[1].Rows[i]["Id"].ToString(),
                        DNTTId = ds.Tables[1].Rows[i]["DNTTId"].ToString(),
                        ChiHoId = ds.Tables[1].Rows[i]["ChiHoId"].ToString(),
                        KiHieuHD = ds.Tables[1].Rows[i]["KiHieuHD"].ToString(),
                        SoHD = ds.Tables[1].Rows[i]["SoHD"].ToString(),
                        NgayHD = ds.Tables[1].Rows[i]["NgayHD"].ToString(),
                        TenNguoiBan = ds.Tables[1].Rows[i]["TenNguoiBan"].ToString(),
                        AWBBILL = ds.Tables[1].Rows[i]["AWBBILL"].ToString(),
                        KhachHang = ds.Tables[1].Rows[i]["KhachHang"].ToString(),
                        TenPhi = ds.Tables[1].Rows[i]["TenPhi"].ToString(),
                        ThanhTien = ds.Tables[1].Rows[i]["ThanhTien"].ToString(),
                    });
                }
            }
            dntt.DnttChiTiets = listChiTiet;
            return dntt;
        }
        
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string DeleteFile(cs.HamDungChung.ajaxGet2 ajaxGet2)
        {
            string ftpFolder = ajaxGet2.get1;
            string fileName = ajaxGet2.get2;

            string kq = "";

            string url = "";

            url = ftp + ftpFolder + "/" + fileName;
            try
            {
                ftpRequest = (FtpWebRequest)WebRequest.Create(url);
                ftpRequest.Credentials = new NetworkCredential(user, pass);
                ftpRequest.UseBinary = true;
                ftpRequest.UsePassive = true;
                ftpRequest.KeepAlive = true;

                ftpRequest.Method = WebRequestMethods.Ftp.DeleteFile;
                ftpResponse = (FtpWebResponse)ftpRequest.GetResponse();

                ftpResponse.Close();
                ftpRequest = null;
                kq = "Đã xóa thành công!";
                
                string[] listFileName = directoryList(ftpFolder);
                int totalFile = 0;
                if (listFileName.Length > 0 && listFileName[0] != "")
                {
                    totalFile = listFileName.Length;
                }
                
                UpdateFileDinhKem(new cs.HamDungChung.ajaxGet2 { get1 = ftpFolder, get2 = totalFile.ToString() });

            }
            catch (Exception ex)
            {
                kq = ex.ToString();
            }

            return kq;
        }

        #endregion Load

        #region Insert Update

        [WebMethod]
        public static string UpdateDuyetThanhToan(cs.HamDungChung.ajaxGet ajaxGet)
        {
            userId = System.Web.HttpContext.Current.Session["WebUID"].ToString();
            try
            {
                pr.ChiHoParameter("6"
                , ajaxGet.get
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , userId
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                , ""
                     );
                siud.UpdateSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo", "swp", pr.pname, pr.pvalue);
            }
            catch (Exception ex)
            {
                return "lỗi";
            }
            return "ok";
        }

        [WebMethod]
        public static string InsertChiHoExcel(List<ChiHo> DataInput)
        {
            List<string> listId = new List<string>();
            try
            {
                foreach (ChiHo chiHo in DataInput)
                {
                  listId.Add(InsertUpdateChiHo(chiHo));
                }
            }
            catch (Exception ex)
            {
                return "lỗi";
            }
            return "ok";
        }

        [WebMethod]
        public static string InsertUpdateChiHo(ChiHo chiHo)
        {
            string kq = "0";
            string iu = "1";
            userId = System.Web.HttpContext.Current.Session["WebUID"].ToString();
            if (chiHo.Id != "")
            {
                iu = "2";
            }
            try
            {
                pr.ChiHoParameter(iu
                           , chiHo.Id
                           , chiHo.NCU
                           , chiHo.LoaiHinh
                           , chiHo.NgayCK
                           , chiHo.KhachHang
                           , chiHo.AWBBILL
                           , chiHo.KiHieuHD
                           , chiHo.SoHD
                           , chiHo.NgayHD
                           , chiHo.TenNguoiBan
                           , chiHo.PhiChungTuNhap
                           , chiHo.SoTruocThue
                           , chiHo.ThanhTien
                           , chiHo.HoaDonKhach
                           , chiHo.Check_ChiHo
                           , chiHo.GhiChu
                           , chiHo.TrangThaiDNTT
                           , chiHo.IdNhap
                           , userId
                           , ""
                           , ""
                           , ""
                           , chiHo.SoDeNghiThanhToan
                           , ""
                           , chiHo.ThanhToanNCC
                           , chiHo.NgayThanhToanNCC
                           , chiHo.NguonDuLieu
                           );
                ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo", "swp", pr.pname, pr.pvalue);
                if (ds.Tables[0].Rows.Count > 0)
                {
                    kq = ds.Tables[0].Rows[0]["Id"].ToString().Trim();
                }
            }
            catch (Exception ex)
            {
                return "-1";
            }
            return kq;
        }

        [WebMethod]
        public static string DeleteChiHo(cs.HamDungChung.ajaxGet ajaxGet)
        {
            try
            {
                pr.ChiHoParameter("3"
                           , ajaxGet.get
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           );
                siud.UpdateSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo", "swp", pr.pname, pr.pvalue);
            }
            catch (Exception ex)
            {
                return "lỗi";
            }
            return "ok";
        }
        [WebMethod]
        public static string InsertUpdateDNTT(DNTTParameter dntt)
        {
            string kq = "0";
            string iu = "1";
            userId = System.Web.HttpContext.Current.Session["WebUID"].ToString();
            if (dntt.Id != "")
            {
                iu = "2";
            }
            try
            {
                pr.DNTTParameter(iu
                           , dntt.Id
                           , dntt.KhachHang
                           , dntt.NoiDungThanhToan
                           , dntt.GhiChu
                           , dntt.ThemChiHoId
                           , dntt.XoaChiHoId
                           , userId
                           );
                ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_DNTT", "swp", pr.pname, pr.pvalue);
                if (ds.Tables[0].Rows.Count > 0)
                {
                    kq = ds.Tables[0].Rows[0]["Id"].ToString().Trim();
                }
                Create_DNTT_ExcelFile(kq);


            }
            catch (Exception ex)
            {
                return "-1";
            }
            return kq;
        }

        [WebMethod]
        public static DNTT DuyetDNTT(cs.HamDungChung.ajaxGet ajaxGet){
            DNTT dnttResult = new DNTT();
            userId = System.Web.HttpContext.Current.Session["WebUID"].ToString();
           try{
               pr.DNTTParameter("5"
                          , ajaxGet.get
                          , ""
                          , ""
                          , ""
                          , ""
                          , ""
                          , userId
                          );
               ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_DNTT", "swp", pr.pname, pr.pvalue);
               if(ds.Tables[0].Rows.Count > 0){
                   dnttResult.DaDuyet = ds.Tables[0].Rows[0]["DaDuyet"].ToString().Trim();
                   dnttResult.Id = ds.Tables[0].Rows[0]["Id"].ToString().Trim();
                   dnttResult.NgayDuyet = ds.Tables[0].Rows[0]["NgayDuyet"].ToString().Trim();
                   dnttResult.NguoiDuyet = ds.Tables[0].Rows[0]["NguoiDuyet"].ToString().Trim();
               }
           }
           catch (Exception ex)
           {
               
           }
           return dnttResult;
        }
        [WebMethod]
        public static string  UpdateFileDinhKem(cs.HamDungChung.ajaxGet2 ajaxGet2)
        {
            try
            {
                pr.ChiHoParameter("7"
                           , ajaxGet2.get1
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ajaxGet2.get2
                           , ""
                           , ""
                           , ""
                           );
                siud.UpdateSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo", "swp", pr.pname, pr.pvalue);
            }
            catch (Exception ex)
            {
                return "lỗi";
            }
            return "ok";
        }

        #endregion Insert Update

        #region load MAWB HAWB

        [WebMethod]
        public static List<string> getBillAWB(cs.HamDungChung.ajaxGet2 ajaxGet2)
        {
            List<string> list_bill_awb = new List<string>();
            pr.ChiHoParameter("5"
                           , ""
                           , ""
                           , ajaxGet2.get1
                           , ""
                           , ajaxGet2.get2
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           , ""
                           );
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo", "swp", pr.pname, pr.pvalue);
            if (ds != null)
            {
                if (ds.Tables[0].Rows.Count > 0)
                {
                    for (int i = 0; i < ds.Tables[0].Rows.Count; i++)
                    {
                        list_bill_awb.Add(ds.Tables[0].Rows[i]["bill_awb"].ToString());
                    }
                }
            }

            return list_bill_awb;
        }

        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static List<FileInfo> reFileDinhKem(cs.HamDungChung.ajaxGet ajaxGet)
        {
            List<FileInfo> fi = new List<FileInfo>();
            folder = (ajaxGet.get).Trim();
            string[] listFileName = directoryList(folder);

            foreach (var fn in listFileName)
            {
                if (fn != "")
                {
                    string url = ftp + folder + "/" + fn;
                    try
                    {
                        
                        FtpWebRequest sizeRequest = (FtpWebRequest)WebRequest.Create(url);
                        sizeRequest.Method = WebRequestMethods.Ftp.GetFileSize;
                        sizeRequest.Credentials = new NetworkCredential(user, pass);

                        using (FtpWebResponse sizeResponse = (FtpWebResponse)sizeRequest.GetResponse())
                        {
                            long fileSize = sizeResponse.ContentLength;
                            fi.Add(new FileInfo
                            {
                                filename = fn,
                                filesize = fileSize.ToString()
                            });
                        }
                    }
                    catch (WebException e)
                    {
                        // Xử lý lỗi nếu cần
                    }
                }
            }

            return fi;
        }

        /* List Directory Contents File/Folder Name Only */

        public static string[] directoryList(string directory)
        {
            try
            {
                /* Create an FTP Request */
                ftpRequest = (FtpWebRequest)FtpWebRequest.Create(ftp + "/" + directory);
                /* Log in to the FTP Server with the User Name and Password Provided */
                ftpRequest.Credentials = new NetworkCredential(user, pass);
                /* When in doubt, use these options */
                ftpRequest.UseBinary = true;
                ftpRequest.UsePassive = true;
                ftpRequest.KeepAlive = true;
                /* Specify the Type of FTP Request */
                ftpRequest.Method = WebRequestMethods.Ftp.ListDirectory;
                /* Establish Return Communication with the FTP Server */
                ftpResponse = (FtpWebResponse)ftpRequest.GetResponse();
                /* Establish Return Communication with the FTP Server */
                ftpStream = ftpResponse.GetResponseStream();
                /* Get the FTP Server's Response Stream */
                StreamReader ftpReader = new StreamReader(ftpStream);
                /* Store the Raw Response */
                string directoryRaw = null;
                /* Read Each Line of the Response and Append a Pipe to Each Line for Easy Parsing */
                try { while (ftpReader.Peek() != -1) { directoryRaw += ftpReader.ReadLine() + "|"; } }
                catch (Exception ex) { Console.WriteLine(ex.ToString()); }
                /* Resource Cleanup */
                ftpReader.Close();
                ftpStream.Close();
                ftpResponse.Close();
                ftpRequest = null;
                /* Return the Directory Listing as a string Array by Parsing 'directoryRaw' with the Delimiter you Append (I use | in This Example) */
                try
                {
                    string[] directoryList = directoryRaw.Split("|".ToCharArray());
                    return directoryList;
                }
                catch (Exception ex)
                {
                    Console.WriteLine(ex.ToString());
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.ToString());
            }

            /* Return an Empty string Array if an Exception Occurs */
            return new string[] { "" };
        }
        [WebMethod]
        public static TaiHoaDonInfo TaiHoaDon(List<Search4> listAwbBill)
        {
            TaiHoaDonInfo taiHoaDonInfo = new TaiHoaDonInfo();
            taiHoaDonInfo.ZipFileName = "error";
            List<TaiHoaDonDetail> listTaiHoaDonDetail = new List<TaiHoaDonDetail>();
            string zipFileName = "error";
            if (listAwbBill.Count > 0){
                string joinedParams = string.Join(",", listAwbBill.Select(awb => awb.param1));
                if(joinedParams != ""){ 
                    pr.Search4Parameter("0"
                           , joinedParams
                           , ""
                           , ""
                           , ""
                           ); 
                    ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_TimKiem", "swp", pr.pname, pr.pvalue);
                    if(ds != null){
                        if(ds.Tables[0].Rows.Count > 0){
                            List<ZipFileInfo> listZipFileInfo = new List<ZipFileInfo>();
                            foreach (DataRow row in ds.Tables[0].Rows)
                            {
                                listTaiHoaDonDetail.Add(new TaiHoaDonDetail{
                                    Id = row["Id"].ToString(),
                                    AWBBILL = row["AWBBILL"].ToString(),
                                    SoHD = row["SoHD"].ToString(),
                                    FileDinhKem = row["FileDinhKem"].ToString()
                                });
                                int fdk = 0;
                                if (row["Id"].ToString() != "" && int.TryParse(row["FileDinhKem"].ToString(), out fdk) && fdk > 0) {
                                    listZipFileInfo.Add(new ZipFileInfo{
                                        FolderId = row["Id"].ToString(),
                                        FolderName = row["AWBBILL"].ToString()
                                    });
                                }
                            }
                            QuanLyChiHo quanLyChiHo = new QuanLyChiHo();
                            zipFileName = quanLyChiHo.DownloadAllFilesAsZip(listZipFileInfo);
                            
                        }
                    }
                    else
                    {
                        zipFileName = "error";
                    }
                }
                else
                {
                    zipFileName = "error";
                }
            }
            else{
                zipFileName = "error";
            }
            taiHoaDonInfo.TaiHoaDonDetails = listTaiHoaDonDetail;
            taiHoaDonInfo.ZipFileName = zipFileName;
            return taiHoaDonInfo;
        }
        [WebMethod]
        public static TaiHoaDonInfo TaiHoaDonByDNTTId(string dnttId)
        {
            TaiHoaDonInfo taiHoaDonInfo = new TaiHoaDonInfo();
            taiHoaDonInfo.ZipFileName = "error";
            List<TaiHoaDonDetail> listTaiHoaDonDetail = new List<TaiHoaDonDetail>();
            string zipFileName = "error";

                
                    pr.Search4Parameter("8"
                           , dnttId
                           , ""
                           , ""
                           , ""
                           ); 
                    ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_TimKiem", "swp", pr.pname, pr.pvalue);
                    if(ds != null){
                        if(ds.Tables[0].Rows.Count > 0){
                            List<ZipFileInfo> listZipFileInfo = new List<ZipFileInfo>();
                            foreach (DataRow row in ds.Tables[0].Rows)
                            {
                                listTaiHoaDonDetail.Add(new TaiHoaDonDetail{
                                    Id = row["Id"].ToString(),
                                    AWBBILL = row["AWBBILL"].ToString(),
                                    SoHD = row["SoHD"].ToString(),
                                    FileDinhKem = row["FileDinhKem"].ToString()
                                });
                                int fdk = 0;
                                if (row["Id"].ToString() != "" && int.TryParse(row["FileDinhKem"].ToString(), out fdk) && fdk > 0) {
                                    listZipFileInfo.Add(new ZipFileInfo{
                                        FolderId = row["Id"].ToString(),
                                        FolderName = row["AWBBILL"].ToString()
                                    });
                                }
                            }
                            QuanLyChiHo quanLyChiHo = new QuanLyChiHo();
                            zipFileName = quanLyChiHo.DownloadAllFilesAsZip(listZipFileInfo);
                            
                        }
                    }
                    else
                    {
                        zipFileName = "error";
                    }
                
            
            taiHoaDonInfo.TaiHoaDonDetails = listTaiHoaDonDetail;
            taiHoaDonInfo.ZipFileName = zipFileName;
            return taiHoaDonInfo;
        }
        public string DownloadAllFilesAsZip(List<ZipFileInfo> listZipFileInfo)
        {
            if(listZipFileInfo.Count > 0){  
                string zipFileName = "error";
                using (ZipFile zip = new ZipFile())
                {
                WebClient request = new WebClient();
                request.Credentials = new NetworkCredential(user, pass);

                // Danh sách các phần mở rộng tệp hợp lệ
                string[] validExtensions = { ".txt", ".pdf", ".jpg", ".jpeg", ".png", ".gif", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx" };

                foreach (var zipFileInfo in listZipFileInfo)
                {
                    string folderUrl = ftp + "/" + zipFileInfo.FolderId + "/";
                    FtpWebRequest listRequest = (FtpWebRequest)WebRequest.Create(folderUrl);
                    listRequest.Method = WebRequestMethods.Ftp.ListDirectory;
                    listRequest.Credentials = new NetworkCredential(user, pass);

                    using (FtpWebResponse listResponse = (FtpWebResponse)listRequest.GetResponse())
                    using (StreamReader reader = new StreamReader(listResponse.GetResponseStream()))
                    {
                        while (!reader.EndOfStream)
                        {
                            string fileName = reader.ReadLine();
                            string fileExtension = Path.GetExtension(fileName).ToLower();

                            // Kiểm tra phần mở rộng tệp
                            if (validExtensions.Contains(fileExtension))
                            {
                                string fileUrl = folderUrl + fileName;
                                byte[] fileData = request.DownloadData(fileUrl);

                                // Thêm tệp vào tệp ZIP
                                //zip.AddEntry(zipFileInfo.FolderName + "/" + fileName, fileData);
                                zip.AddEntry(fileName, fileData);
                            }
                        }
                    }
                }

                // Đặt tên tệp ZIP theo thời gian hiện tại
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                zipFileName = $"files_{timestamp}.zip";

                using (MemoryStream ms = new MemoryStream())
                {
                    zip.Save(ms);
                    byte[] zipBytes = ms.ToArray(); // Chuyển đổi MemoryStream thành byte[]
                    HttpRuntime.Cache.Insert(
                        zipFileName,
                        zipBytes, // Lưu trữ byte[] thay vì MemoryStream
                        null,
                        Cache.NoAbsoluteExpiration,
                        TimeSpan.FromMinutes(10));
                }
                }
                return zipFileName;
            }
            else{
                return "error";
            }
        }

        
        public static void Create_DNTT_ExcelFile(string dnttId)
        {

                    pr.DNTTParameter("4"
                                    , dnttId
                                    , ""
                                    , ""
                                    , ""
                                    , ""
                                    , ""
                                    , ""
                                    );
                    ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_DNTT", "swp", pr.pname, pr.pvalue);
                    
                    // Người đề nghị: E10
            // Số tiền: = ô SUM tổng
            // Số DNTT: E17
            // Dòng bắt đầu E21
            // Tổng cột H
            string folder_id = dnttId;
            WebClient request = new WebClient();
            string url = "";
            byte[] newFileData = null;
         
            request.Credentials = new NetworkCredential(cs.ftp._user, cs.ftp._pass);
            url = ftp + "Templates" + "/" + "DNTT.xlsx";
            try
            {
                newFileData = request.DownloadData(url);
            }
            catch (WebException ex)
            {
                throw new Exception((ex.Response as FtpWebResponse).StatusDescription);
            }
             using (var excelPackage = new ExcelPackage(new MemoryStream(newFileData)))
            {
                excelPackage.Workbook.Properties.Author = "KENJI";
                excelPackage.Workbook.Properties.Title = "Đề nghị thanh toán";
                excelPackage.Workbook.Properties.Comments = "";
                var workSheet1 = excelPackage.Workbook.Worksheets[1];
                int DNTTDetailCount = ds.Tables[1].Rows.Count;
                int startRow = 19;
                int currentRow = 19;
                // Thông tin chung
                if (ds.Tables[0].Rows.Count > 0)
                {
                    string tenNguoiTao = ds.Tables[0].Rows[0]["TenNguoiTao"].ToString();
                    workSheet1.Cells["B10"].Value = "Họ và tên người đề nghị thanh toán: " + tenNguoiTao;
                    workSheet1.Cells["H28"].Value = tenNguoiTao;
                    workSheet1.Cells["E13"].Value = ds.Tables[0].Rows[0]["NoiDungThanhToan"].ToString();
                    workSheet1.Cells["D12"].Value = ds.Tables[0].Rows[0]["Id"].ToString();
                    
                }
                if(DNTTDetailCount > 0){
                    workSheet1.InsertRow(currentRow, DNTTDetailCount);
                    // bắt đầu từ dòng 19
                    int tableRow = 1;
                    decimal sumThanhTien = 0;
                    foreach (DataRow item in ds.Tables[1].Rows)
                    {
                        workSheet1.Cells["B" + currentRow.ToString()].Value = tableRow;
                        workSheet1.Cells["C" + currentRow.ToString()].Value = item["KiHieuHD"].ToString();
                        workSheet1.Cells["D" + currentRow.ToString()].Value = item["SoHD"].ToString();
                        workSheet1.Cells["E" + currentRow.ToString()].Value = returnDateTimeddMMyyyy(item["NgayHD"].ToString())[0];
                        workSheet1.Cells["E" + currentRow.ToString()].Style.Numberformat.Format = "dd/MM/yyyy";
                        workSheet1.Cells["F" + currentRow.ToString()].Value = item["TenNguoiBan"].ToString();
                        workSheet1.Cells["F" + currentRow.ToString()].Style.WrapText = true;
                        workSheet1.Cells["G" + currentRow.ToString()].Value = item["TenPhi"].ToString() + " " + item["KhachHang"].ToString() + " " + item["AWBBILL"].ToString();
                        workSheet1.Cells["G" + currentRow.ToString()].Style.WrapText = true;
                        workSheet1.Cells["H" + currentRow.ToString()].Value = decimal.Parse(item["ThanhTien"].ToString());
                        workSheet1.Cells["H" + currentRow.ToString()].Style.Numberformat.Format = "#,##0";
                        sumThanhTien += decimal.Parse(item["ThanhTien"].ToString());
                        currentRow++;
                        tableRow++;
                    }
                    
                    // // bắt đầu từ dòng 19
                    // int tableRow = 1;
                    // decimal sumThanhTien = 0;
                    // string tempSoHD = "";
                    // List<string> tempKhachHang = new List<string>();
                    // List<string> tempAWBBILL = new List<string>();
                    // List<string> tempTenPhi = new List<string>();
                    // decimal tempThanhTien = 0;
                    // foreach (DataRow item in ds.Tables[1].Rows)
                    // {
                    //     if(item["SoHD"].ToString() != tempSoHD){
                    //         workSheet1.InsertRow(currentRow, 1);
                    //         tempKhachHang.Clear();
                    //         tempAWBBILL.Clear();
                    //         tempTenPhi.Clear();
                    //         tempSoHD = item["SoHD"].ToString();
                    //         tempKhachHang.Add(item["KhachHang"].ToString());
                    //         tempAWBBILL.Add(item["AWBBILL"].ToString());
                    //         tempTenPhi.Add(item["TenPhi"].ToString());
                    //         tempThanhTien = decimal.Parse(item["ThanhTien"].ToString());
                    //         workSheet1.Cells["B" + currentRow.ToString()].Value = tableRow;
                    //         workSheet1.Cells["C" + currentRow.ToString()].Value = item["KiHieuHD"].ToString();
                    //         workSheet1.Cells["D" + currentRow.ToString()].Value = item["SoHD"].ToString();
                    //         workSheet1.Cells["E" + currentRow.ToString()].Value = returnDateTimeddMMyyyy(item["NgayHD"].ToString())[0];
                    //         workSheet1.Cells["E" + currentRow.ToString()].Style.Numberformat.Format = "dd/MM/yyyy";
                    //         workSheet1.Cells["F" + currentRow.ToString()].Value = item["TenNguoiBan"].ToString();
                    //         workSheet1.Cells["F" + currentRow.ToString()].Style.WrapText = true;
                    //         workSheet1.Cells["G" + currentRow.ToString()].Value = item["TenPhi"].ToString() + " " + item["KhachHang"].ToString() + " " + item["AWBBILL"].ToString();
                    //         workSheet1.Cells["G" + currentRow.ToString()].Style.WrapText = true;
                    //         workSheet1.Cells["H" + currentRow.ToString()].Value = decimal.Parse(item["ThanhTien"].ToString());
                    //         workSheet1.Cells["H" + currentRow.ToString()].Style.Numberformat.Format = "#,##0";
                    //         sumThanhTien += decimal.Parse(item["ThanhTien"].ToString());
                    //         currentRow++;
                    //         tableRow++;
                            
                    //     }
                    //     else{
                    //         if (!tempKhachHang.Contains(item["KhachHang"].ToString()))
                    //         {
                    //             tempKhachHang.Add(item["KhachHang"].ToString());
                    //         }
                    //         if (!tempAWBBILL.Contains(item["AWBBILL"].ToString()))
                    //         {
                    //             tempAWBBILL.Add(item["AWBBILL"].ToString());
                    //         }
                    //         if (!tempTenPhi.Contains(item["TenPhi"].ToString()))
                    //         {
                    //             tempTenPhi.Add(item["TenPhi"].ToString());
                    //         }
                    //         workSheet1.Cells["G" + (currentRow - 1).ToString()].Value = string.Join("\n", tempTenPhi) + " " + string.Join("-", tempKhachHang) + " " + string.Join("\n", tempAWBBILL);
                    //         tempThanhTien += decimal.Parse(item["ThanhTien"].ToString());
                    //         workSheet1.Cells["H" + (currentRow - 1).ToString()].Value = tempThanhTien;
                    //         workSheet1.Cells["H" + (currentRow - 1).ToString()].Style.Numberformat.Format = "#,##0";
                    //         sumThanhTien += decimal.Parse(item["ThanhTien"].ToString());
                    //     }
                    // }
                    workSheet1.Cells["H" +currentRow.ToString()].Formula = "=SUM(H" + startRow.ToString() + ":H" + (currentRow-1).ToString() + ")"; 
                    
                    workSheet1.Cells["D14"].Formula = "=H" +currentRow.ToString(); // Số tiền = cell tổng
                    workSheet1.Cells["D14"].Style.Numberformat.Format = "#,##0";
                    workSheet1.Cells["D15"].Value = ConvertAmountToWords(sumThanhTien);

                    using (var ranger = workSheet1.Cells["B" + startRow.ToString() + ":H" + currentRow.ToString()])
                    {
                        ranger.Style.Font.Name = "Times New Roman";
                        ranger.Style.Font.Size = 11;
                        ranger.Style.Border.Top.Style = ExcelBorderStyle.Thin;
                        ranger.Style.Border.Left.Style = ExcelBorderStyle.Thin;
                        ranger.Style.Border.Right.Style = ExcelBorderStyle.Thin;
                        ranger.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                        ranger.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
                        ranger.Style.VerticalAlignment = OfficeOpenXml.Style.ExcelVerticalAlignment.Center;
                        //ranger.AutoFitColumns(5);
                    }
                    using (var ranger = workSheet1.Cells["H" + startRow.ToString() + ":H" + currentRow.ToString()])
                    {
                        ranger.Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Right;
                    }
                }
                
                excelPackage.Save();
                var stream =  excelPackage.Stream;
                MemoryStream pdfStream = new MemoryStream();

                Workbook workbook = new Workbook();
                workbook.LoadFromStream(stream);

                workbook.SaveToStream(pdfStream, FileFormat.PDF);
                pdfStream.Position = 0;

                try
                {
                    string ftp_folder_dntt = ftp + "DNTT/";
                    if (cs.HamDungChung.CreateFTPDirectory(ftp_folder_dntt, folder_id, user, pass) == false)
                    {
                    }
                    var xlsxBuffer = stream as MemoryStream;
                    var pdfBuffer = pdfStream as MemoryStream;
                    Byte[] xlsxBuffer_ = xlsxBuffer.ToArray();
                    int bytesRead = xlsxBuffer_.Length;
                    //xlsx
                    FtpWebRequest request2 = (FtpWebRequest)WebRequest.Create(ftp_folder_dntt + folder_id  + "/" + "DNTT" + folder_id + ".xlsx");
                    request2.Method = WebRequestMethods.Ftp.UploadFile;
                    request2.Credentials = new NetworkCredential(user, pass);
                    request2.ContentLength = bytesRead;
                    request2.UsePassive = true;
                    request2.UseBinary = true;
                    request2.ServicePoint.ConnectionLimit = bytesRead;
                    request2.EnableSsl = false;
                    using (Stream requestStream = request2.GetRequestStream())
                    {

                        requestStream.Write(xlsxBuffer_, 0, bytesRead);
                        requestStream.Close();
                    }
                    FtpWebResponse response2 = (FtpWebResponse)request2.GetResponse();

                    response2.Close();
                    //pdf
                    Byte[] pdfBuffer_ = pdfBuffer.ToArray();
                    bytesRead = pdfBuffer_.Length;
                    FtpWebRequest request3 = (FtpWebRequest)WebRequest.Create(ftp_folder_dntt + folder_id  + "/" + "DNTT" + folder_id + ".pdf");
                    request3.Method = WebRequestMethods.Ftp.UploadFile;
                    request3.Credentials = new NetworkCredential(user, pass);
                    request3.ContentLength = bytesRead;
                    request3.UsePassive = true;
                    request3.UseBinary = true;
                    request3.ServicePoint.ConnectionLimit = bytesRead;
                    request3.EnableSsl = false;
                    using (Stream requestStream = request3.GetRequestStream())
                    {

                        requestStream.Write(pdfBuffer_, 0, bytesRead);
                        requestStream.Close();
                    }
                    FtpWebResponse response3 = (FtpWebResponse)request2.GetResponse();

                    response3.Close();
                }
                catch (WebException ex)
                {
                    throw new Exception((ex.Response as FtpWebResponse).StatusDescription);
                }
            }
            
            
        }
       
        [WebMethod]
        public static void ViewPdfFile(string dnttId)
        {
            string fileName = "DNTT" + dnttId + ".pdf";
            string fileUrl = ftp + "DNTT/" + dnttId + "/" + fileName;
            FtpWebRequest request = (FtpWebRequest)WebRequest.Create(fileUrl);
            request.Method = WebRequestMethods.Ftp.DownloadFile;
            request.Credentials = new NetworkCredential(user, pass);

            using (FtpWebResponse response = (FtpWebResponse)request.GetResponse())
            using (Stream responseStream = response.GetResponseStream())
            {
                HttpContext.Current.Response.ContentType = "application/pdf";
                HttpContext.Current.Response.AddHeader("content-disposition", "inline; filename=" + fileName);
                responseStream.CopyTo(HttpContext.Current.Response.OutputStream);
                HttpContext.Current.Response.Flush();
                HttpContext.Current.Response.End();
            }
        }
        [WebMethod]
        public static List<string> KiemTraHoaDon(Search5 search5)
        {
            ChiHo chiHo = new ChiHo();
            List<string> result = new List<string>();
            pr.Search5Parameter("6"
                        , search5.param1
                        , search5.param2
                        , search5.param3
                        , search5.param4
                        , search5.param5
                        ); 
            ds = siud.ScripSIUD(cs.HamDungChung._connectionStringCARGO, "Web_S_ThanhToan_ChiHo_TimKiem", "swp", pr.pname, pr.pvalue);
            if(ds != null && ds.Tables.Count > 0){
                if(ds.Tables[0].Rows.Count > 0){
                    foreach (DataRow row in ds.Tables[0].Rows)
                    {
                        result.Add(row["code"].ToString());
                        result.Add(row["result"].ToString());
                        result.Add(search5.param1);
                        result.Add(search5.param2);
                        result.Add(row["SoTienTruocThue"].ToString());
                        result.Add(row["SoTienSauThue"].ToString());
                        result.Add(row["reason"].ToString());
                    }
                    
                }    
            }else{
                result.Add("error");
            }
            return result;
        }
        
        
        public static string[] returnDateTimeddMMyyyy(string dt)
        {
            string[] re_dt = new String[2];
            if (dt != "")
            {
                string[] arr_date = dt.Split(' ')[0].Split('/');
                string[] arr_time = dt.Split(' ')[1].Split(':');
                int timeAMPM = 0;

                timeAMPM = int.Parse(arr_time[0]);
                if (dt.Split(' ')[2] == "PM" && int.Parse(arr_time[0]) < 12)
                {
                    timeAMPM = int.Parse(arr_time[0]) + 12;
                }
                re_dt[0] = convert2ChuSo(arr_date[1]) + "/" + convert2ChuSo(arr_date[0]) + "/" + convert2ChuSo(arr_date[2]);
                re_dt[1] = convert2ChuSo(timeAMPM.ToString()) + ":" + convert2ChuSo(arr_time[1]);

                if (dt.Split(' ')[0] == "1/1/1900")
                {
                    re_dt[0] = "-";
                    re_dt[1] = "-";
                }
            }

            return re_dt;
        }

        public static string convert2ChuSo(string string1chu)
        {
            if (string1chu.Trim().Length < 2)
            {
                string1chu = "0" + string1chu;
            }
            return string1chu;
        }
            public static string ConvertAmountToWords(decimal amount)
    {
        if (amount == 0)
            return "Không đồng";

        string[] units = { "", "nghìn", "triệu", "tỷ" };
        string[] tens = { "", "mười", "hai mươi", "ba mươi", "bốn mươi", "năm mươi", "sáu mươi", "bảy mươi", "tám mươi", "chín mươi" };
        string[] ones = { "", "một", "hai", "ba", "bốn", "năm", "sáu", "bảy", "tám", "chín" };

        string result = "";
        int unitIndex = 0;

        long integerPart = (long)amount;
        int fractionalPart = (int)((amount - integerPart) * 100);

        while (integerPart > 0)
        {
            int threeDigits = (int)(integerPart % 1000);
            integerPart /= 1000;

            if (threeDigits > 0)
            {
                string threeDigitsInWords = ConvertThreeDigitsToWords(threeDigits, ones, tens);
                result = threeDigitsInWords + " " + units[unitIndex] + " " + result;
            }

            unitIndex++;
        }

        result = result.Trim() + " đồng";

        if (fractionalPart > 0)
        {
            result += " và " + ConvertThreeDigitsToWords(fractionalPart, ones, tens) + " xu";
        }
        // Viết hoa ký tự đầu tiên
        if (!string.IsNullOrEmpty(result))
        {
            result = char.ToUpper(result[0]) + result.Substring(1);
        }
        return result.Trim();
    }

    private static string ConvertThreeDigitsToWords(int number, string[] ones, string[] tens)
    {
        int hundred = number / 100;
        int ten = (number % 100) / 10;
        int one = number % 10;

        string result = "";

        if (hundred > 0)
        {
            result += ones[hundred] + " trăm ";
        }

        if (ten > 0)
        {
            result += tens[ten] + " ";
        }
        else if (hundred > 0 && one > 0)
        {
            result += "lẻ ";
        }

        if (one > 0)
        {
            if (ten == 1 && one == 5)
            {
                result += "lăm";
            }
            else
            {
                result += ones[one];
            }
        }

        return result.Trim();
    }
        #endregion load MAWB HAWB

        #region Class
        public class dataChiHo{
            public List<ChiHo> chiHos { get; set; }
            public string HdChuaDK { get; set; }
            public string HdKhongNCC { get; set; }
            public string HdKhongSoHD { get; set; }

        }
        public class ChiHo
        {
            public string Id { get; set; }
            public string NCU { get; set; }
            public string LoaiHinh { get; set; }
            public string NgayCK { get; set; }
            public string KhachHang { get; set; }
            public string AWBBILL { get; set; }
            public string KiHieuHD { get; set; }
            public string SoHD { get; set; }
            public string NgayHD { get; set; }
            public string TenNguoiBan { get; set; }
            public string PhiChungTuNhap { get; set; }
            public string SoTruocThue { get; set; }
            public string ThanhTien { get; set; }
            public string HoaDonKhach { get; set; }
            public string Check_ChiHo { get; set; }
            public string GhiChu { get; set; }
            public string TrangThaiDNTT { get; set; }
            public string IdNhap { get; set; }
            public string TrangThaiDoiChieuKhach { get; set; }
            public string DuyetThanhToan { get; set; }
            public string SoDeNghiThanhToan { get; set; }
            public string FileDinhKem { get; set; }
            public string ThanhToanNCC { get; set; }
            public string NgayThanhToanNCC { get; set; }
            public string NguonDuLieu { get; set; }
            public string NguoiDuyetThanhToanNCC { get; set; }
            public string Check_HoaDon { get; set; }
            public string NgayTao { get; set; }
        }

        public class KhachHangChiHo
        {
            public string KhachHang { get; set; }
            public string LoaiHinh { get; set; }
        }

        public class MAWBHAWB
        {
            public List<MawbChiHo> mawbChiHos { get; set; }
            public List<HawbChiHo> hawbChiHos { get; set; }
        }

        public class MawbChiHo
        {
            public string SoMAWB { get; set; }
        }

        public class HawbChiHo
        {
            public string HAWB { get; set; }
        }

        public class FileInfo
        {
            public string filename { get; set; }
            public string filesize { get; set; }
            public string filebyte { get; set; }
            public string filethump { get; set; }
        }
        public class Search4
        {
            public string param1 { get; set; }
            public string param2 { get; set; }
            public string param3 { get; set; }
            public string param4 { get; set; }
        }
        public class Search5
        {
            public string param1 { get; set; }
            public string param2 { get; set; }
            public string param3 { get; set; }
            public string param4 { get; set; }
            public string param5 { get; set; }
        }
        public class TaiHoaDonInfo
        {
            public string ZipFileName { get; set; }
            public List<TaiHoaDonDetail> TaiHoaDonDetails { get; set; }
        }
        public class TaiHoaDonDetail
        {
            public string Id { get; set; }
            public string AWBBILL { get; set; }
            public string SoHD { get; set; }
            public string FileDinhKem { get; set; }
        }
        public class ZipFileInfo
        {
            public string FolderId { get; set; }
            public string FolderName { get; set; }
        }
        public class DNTT
        {
            public string Id { get; set; }
            public string KhachHang { get; set; }
            public string NoiDungThanhToan { get; set; }
            public string GhiChu { get; set; }
            public string HienThi { get; set; }
            public string NguoiTao { get; set; }
            public string NgayTao { get; set; }
            public string NguoiSua { get; set; }
            public string NgaySua { get; set; }
            public string TenNguoiTao { get; set; }
            public string ThemChiHoId { get; set; }
            public string XoaChiHoId { get; set; }
            public string DaDuyet { get; set; }
            public string NgayDuyet { get; set; }
            public string NguoiDuyet { get; set; }
            public List<DNTTChiTiet> DnttChiTiets { get; set; } = new List<DNTTChiTiet>();

        }
        public class DNTTChiTiet
        {
            public string Id { get; set; }
            public string DNTTId { get; set; }
            public string ChiHoId { get; set; }
            public bool HienThi { get; set; }
            public string NguoiTao { get; set; }
            public string NgayTao { get; set; }
            public string NguoiSua { get; set; }
            public string NgaySua { get; set; }
            public string KiHieuHD { get; set; }
            public string SoHD { get; set; }
            public string NgayHD { get; set; }
            public string TenNguoiBan { get; set; }
            public string AWBBILL { get; set; }
            public string KhachHang { get; set; }
            public string TenPhi { get; set; }
            public string ThanhTien { get; set; }
        }
        public class DNTTParameter
        {
            public string Id { get; set; }
            public string KhachHang { get; set; }
            public string NoiDungThanhToan { get; set; }
            public string GhiChu { get; set; }
            public string ThemChiHoId { get; set; }
            public string XoaChiHoId { get; set; }
        }


        public class DNTTChiTietParameter
        {
            public string Id { get; set; }
            public string DNTTId { get; set; }
            public string ChiHoId { get; set; }
        }
        #endregion Class
    }
   
}